# Task Scheduling Service

A basic task scheduling service built with Express.js that allows you to schedule API calls to third-party services and manage their execution through worker processes.

## Features

- **Task Creation**: Schedule API calls with specific execution times
- **Worker Integration**: Workers can query for available tasks and execute them
- **Task Management**: Track task status (pending, claimed, in-progress, completed, failed)
- **Retry Logic**: Automatic retry for failed tasks (up to 3 attempts)
- **In-Memory Storage**: Simple in-memory task persistence (suitable for development/testing)
- **RESTful API**: Clean REST endpoints for all operations

## Installation

```bash
npm install
```

## Usage

### Starting the Server

```bash
node server.js
```

The server will start on port 3000 (or the port specified in the `PORT` environment variable).

### Starting a Worker

```bash
node worker.js
```

The worker will continuously poll for available tasks and execute them.

### Running the Demo

```bash
node test-client.js
```

Make sure the server is running before executing the demo.

## API Endpoints

### Task Creation

**POST /tasks**

Create a new scheduled task.

```json
{
  "apiUrl": "https://api.example.com/endpoint",
  "payload": {
    "key": "value"
  },
  "executeAt": "2024-01-01T12:00:00.000Z",
  "method": "POST",
  "headers": {
    "Authorization": "Bearer token",
    "Content-Type": "application/json"
  }
}
```

**Response:**
```json
{
  "message": "Task created successfully",
  "taskId": "uuid-here",
  "executeAt": "2024-01-01T12:00:00.000Z",
  "status": "pending"
}
```

### Worker Endpoints

**GET /tasks/available**

Get available tasks ready for execution.

Query parameters:
- `limit` (optional): Maximum number of tasks to return (default: 10)

**POST /tasks/:taskId/claim**

Claim a task for execution (prevents other workers from picking it up).

**PUT /tasks/:taskId/status**

Update task status.

```json
{
  "status": "completed|failed|in-progress",
  "error": "Error message (for failed tasks)",
  "response": {
    "status": 200,
    "data": "API response data"
  }
}
```

### Task Management

**GET /tasks/:taskId**

Get detailed information about a specific task.

**GET /tasks**

List all tasks with optional filtering.

Query parameters:
- `status` (optional): Filter by task status
- `limit` (optional): Maximum number of tasks to return (default: 50)

**POST /tasks/:taskId/execute**

Manually execute a task (useful for testing).

### Health Check

**GET /health**

Check service health and get basic statistics.

## Task Status Flow

1. **pending**: Task is created and waiting for execution time
2. **claimed**: Task has been claimed by a worker
3. **in-progress**: Task is currently being executed
4. **completed**: Task executed successfully
5. **failed**: Task execution failed (can be retried up to 3 times)

## Task Structure

```javascript
{
  "id": "uuid",
  "apiUrl": "https://api.example.com/endpoint",
  "payload": { /* request payload */ },
  "method": "POST|GET|PUT|DELETE",
  "headers": { /* request headers */ },
  "executeAt": "2024-01-01T12:00:00.000Z",
  "status": "pending|claimed|in-progress|completed|failed",
  "createdAt": "2024-01-01T11:00:00.000Z",
  "claimedAt": "2024-01-01T12:00:00.000Z",
  "completedAt": "2024-01-01T12:00:05.000Z",
  "error": "Error message if failed",
  "retryCount": 0,
  "maxRetries": 3
}
```

## Example Usage

### Creating a Task

```javascript
const axios = require('axios');

const task = await axios.post('http://localhost:3000/tasks', {
  apiUrl: 'https://jsonplaceholder.typicode.com/posts',
  payload: {
    title: 'My Post',
    body: 'Post content',
    userId: 1
  },
  executeAt: new Date(Date.now() + 60000).toISOString(), // Execute in 1 minute
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  }
});

console.log('Task created:', task.data.taskId);
```

### Worker Implementation

The included `worker.js` demonstrates how to implement a worker that:

1. Polls for available tasks
2. Claims tasks to prevent duplicate processing
3. Executes the third-party API calls
4. Updates task status based on results
5. Handles errors and retries

## Architecture

- **Express.js Server**: Handles HTTP requests and task management
- **In-Memory Storage**: Uses a Map to store tasks (can be replaced with a database)
- **Worker Pattern**: Separate worker processes poll for and execute tasks
- **RESTful Design**: Clean API design following REST principles

## Development

### Project Structure

```
├── server.js          # Main Express server
├── worker.js          # Example worker implementation
├── test-client.js     # Demo/testing client
├── package.json       # Dependencies
└── README.md          # This file
```

### Dependencies

- **express**: Web framework
- **axios**: HTTP client for making API calls
- **uuid**: Generate unique task IDs

## Future Enhancements

- Database persistence (PostgreSQL, MongoDB, etc.)
- Task scheduling with cron expressions
- Task priorities and queues
- Web dashboard for monitoring
- Webhook notifications
- Task dependencies
- Distributed worker coordination
- Metrics and logging

## License

ISC
