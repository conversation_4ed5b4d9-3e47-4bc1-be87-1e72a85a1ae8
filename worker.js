const axios = require('axios');

class TaskWorker {
  constructor(serverUrl = 'http://localhost:3000', pollInterval = 5000) {
    this.serverUrl = serverUrl;
    this.pollInterval = pollInterval;
    this.isRunning = false;
  }

  async start() {
    console.log('Starting task worker...');
    this.isRunning = true;
    
    while (this.isRunning) {
      try {
        await this.processTasks();
        await this.sleep(this.pollInterval);
      } catch (error) {
        console.error('Error in worker loop:', error.message);
        await this.sleep(this.pollInterval);
      }
    }
  }

  stop() {
    console.log('Stopping task worker...');
    this.isRunning = false;
  }

  async processTasks() {
    try {
      // Get available tasks
      const response = await axios.get(`${this.serverUrl}/tasks/available?limit=5`);
      const { tasks } = response.data;

      if (tasks.length === 0) {
        console.log('No tasks available');
        return;
      }

      console.log(`Found ${tasks.length} available tasks`);

      // Process each task
      for (const task of tasks) {
        await this.processTask(task);
      }

    } catch (error) {
      console.error('Error fetching available tasks:', error.message);
    }
  }

  async processTask(task) {
    try {
      console.log(`Processing task ${task.id}...`);

      // Claim the task
      await axios.post(`${this.serverUrl}/tasks/${task.id}/claim`);
      console.log(`Claimed task ${task.id}`);

      // Mark as in progress
      await axios.put(`${this.serverUrl}/tasks/${task.id}/status`, {
        status: 'in-progress'
      });

      // Execute the third-party API call
      const axiosConfig = {
        method: task.method,
        url: task.apiUrl,
        headers: task.headers,
        timeout: 30000
      };

      if (task.method !== 'GET' && task.payload) {
        axiosConfig.data = task.payload;
      }

      console.log(`Executing API call to ${task.apiUrl}...`);
      const apiResponse = await axios(axiosConfig);

      // Mark task as completed
      await axios.put(`${this.serverUrl}/tasks/${task.id}/status`, {
        status: 'completed',
        response: {
          status: apiResponse.status,
          data: apiResponse.data,
          headers: apiResponse.headers
        }
      });

      console.log(`Task ${task.id} completed successfully`);

    } catch (error) {
      console.error(`Task ${task.id} failed:`, error.message);

      try {
        // Mark task as failed
        await axios.put(`${this.serverUrl}/tasks/${task.id}/status`, {
          status: 'failed',
          error: error.response 
            ? `HTTP ${error.response.status}: ${error.response.statusText}`
            : error.message
        });
      } catch (updateError) {
        console.error(`Failed to update task status:`, updateError.message);
      }
    }
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// If this file is run directly, start the worker
if (require.main === module) {
  const worker = new TaskWorker();
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\nReceived SIGINT, shutting down gracefully...');
    worker.stop();
    process.exit(0);
  });

  process.on('SIGTERM', () => {
    console.log('\nReceived SIGTERM, shutting down gracefully...');
    worker.stop();
    process.exit(0);
  });

  worker.start().catch(error => {
    console.error('Worker failed to start:', error);
    process.exit(1);
  });
}

module.exports = TaskWorker;
