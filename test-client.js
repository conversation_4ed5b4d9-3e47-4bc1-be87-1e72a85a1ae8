const axios = require('axios');

const SERVER_URL = 'http://localhost:3000';

class TestClient {
  constructor() {
    this.baseURL = SERVER_URL;
  }

  async createTask(apiUrl, payload, executeAt, method = 'POST', headers = {}) {
    try {
      const response = await axios.post(`${this.baseURL}/tasks`, {
        apiUrl,
        payload,
        executeAt,
        method,
        headers
      });
      
      console.log('Task created:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error creating task:', error.response?.data || error.message);
      throw error;
    }
  }

  async getAvailableTasks(limit = 10) {
    try {
      const response = await axios.get(`${this.baseURL}/tasks/available?limit=${limit}`);
      console.log('Available tasks:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching available tasks:', error.response?.data || error.message);
      throw error;
    }
  }

  async getTask(taskId) {
    try {
      const response = await axios.get(`${this.baseURL}/tasks/${taskId}`);
      console.log('Task details:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching task:', error.response?.data || error.message);
      throw error;
    }
  }

  async getAllTasks(status = null, limit = 50) {
    try {
      const params = { limit };
      if (status) params.status = status;
      
      const response = await axios.get(`${this.baseURL}/tasks`, { params });
      console.log('All tasks:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching all tasks:', error.response?.data || error.message);
      throw error;
    }
  }

  async claimTask(taskId) {
    try {
      const response = await axios.post(`${this.baseURL}/tasks/${taskId}/claim`);
      console.log('Task claimed:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error claiming task:', error.response?.data || error.message);
      throw error;
    }
  }

  async updateTaskStatus(taskId, status, error = null, response = null) {
    try {
      const payload = { status };
      if (error) payload.error = error;
      if (response) payload.response = response;

      const result = await axios.put(`${this.baseURL}/tasks/${taskId}/status`, payload);
      console.log('Task status updated:', result.data);
      return result.data;
    } catch (error) {
      console.error('Error updating task status:', error.response?.data || error.message);
      throw error;
    }
  }

  async executeTask(taskId) {
    try {
      const response = await axios.post(`${this.baseURL}/tasks/${taskId}/execute`);
      console.log('Task executed:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error executing task:', error.response?.data || error.message);
      throw error;
    }
  }

  async healthCheck() {
    try {
      const response = await axios.get(`${this.baseURL}/health`);
      console.log('Health check:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error checking health:', error.response?.data || error.message);
      throw error;
    }
  }
}

// Demo function to show how to use the service
async function runDemo() {
  const client = new TestClient();

  try {
    console.log('=== Task Scheduling Service Demo ===\n');

    // Health check
    console.log('1. Health Check:');
    await client.healthCheck();
    console.log();

    // Create some test tasks
    console.log('2. Creating test tasks:');
    
    // Task 1: Call JSONPlaceholder API (immediate execution)
    const task1 = await client.createTask(
      'https://jsonplaceholder.typicode.com/posts',
      {
        title: 'Test Post',
        body: 'This is a test post created by the task scheduler',
        userId: 1
      },
      new Date().toISOString(), // Execute immediately
      'POST',
      { 'Content-Type': 'application/json' }
    );

    // Task 2: Call JSONPlaceholder API (scheduled for 10 seconds from now)
    const futureTime = new Date(Date.now() + 10000).toISOString();
    const task2 = await client.createTask(
      'https://jsonplaceholder.typicode.com/posts/1',
      null,
      futureTime,
      'GET'
    );

    // Task 3: Call a non-existent API (will fail)
    const task3 = await client.createTask(
      'https://httpbin.org/status/500',
      { test: 'data' },
      new Date().toISOString(),
      'POST'
    );

    console.log();

    // Get available tasks
    console.log('3. Getting available tasks:');
    await client.getAvailableTasks();
    console.log();

    // Get all tasks
    console.log('4. Getting all tasks:');
    await client.getAllTasks();
    console.log();

    // Execute a task manually (for demonstration)
    console.log('5. Executing task manually:');
    await client.executeTask(task1.taskId);
    console.log();

    // Check task status
    console.log('6. Checking task status:');
    await client.getTask(task1.taskId);
    console.log();

    console.log('=== Demo completed ===');
    console.log('You can now:');
    console.log('- Start the worker with: node worker.js');
    console.log('- Create more tasks using the API endpoints');
    console.log('- Monitor task execution');

  } catch (error) {
    console.error('Demo failed:', error.message);
  }
}

// If this file is run directly, run the demo
if (require.main === module) {
  console.log('Make sure the server is running (node server.js) before running this demo.\n');
  
  // Wait a moment for user to read the message
  setTimeout(() => {
    runDemo();
  }, 2000);
}

module.exports = TestClient;
