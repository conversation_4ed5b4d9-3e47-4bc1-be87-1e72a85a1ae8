{"name": "task-scheduling-service", "version": "1.0.0", "description": "A basic task scheduling service built with Express.js", "main": "server.js", "scripts": {"start": "node server.js", "worker": "node worker.js", "demo": "node test-client.js", "dev": "node server.js", "test": "node test.js", "example": "node example.js"}, "keywords": ["task-scheduler", "express", "api", "worker", "scheduling"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"axios": "^1.11.0", "express": "^5.1.0", "uuid": "^11.1.0"}}