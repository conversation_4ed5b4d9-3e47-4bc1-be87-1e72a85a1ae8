const express = require('express');
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json());

// In-memory task storage
const tasks = new Map();

// Task status constants
const TASK_STATUS = {
  PENDING: 'pending',
  CLAIMED: 'claimed',
  IN_PROGRESS: 'in-progress',
  COMPLETED: 'completed',
  FAILED: 'failed'
};

// Task class to represent a scheduled task
class Task {
  constructor(apiUrl, payload, executeAt, method = 'POST', headers = {}) {
    this.id = uuidv4();
    this.apiUrl = apiUrl;
    this.payload = payload;
    this.method = method.toUpperCase();
    this.headers = headers;
    this.executeAt = new Date(executeAt);
    this.status = TASK_STATUS.PENDING;
    this.createdAt = new Date();
    this.claimedAt = null;
    this.completedAt = null;
    this.error = null;
    this.retryCount = 0;
    this.maxRetries = 3;
  }

  claim() {
    this.status = TASK_STATUS.CLAIMED;
    this.claimedAt = new Date();
  }

  markInProgress() {
    this.status = TASK_STATUS.IN_PROGRESS;
  }

  markCompleted(response) {
    this.status = TASK_STATUS.COMPLETED;
    this.completedAt = new Date();
    this.response = response;
  }

  markFailed(error) {
    this.status = TASK_STATUS.FAILED;
    this.error = error;
    this.retryCount++;
  }

  canRetry() {
    return this.retryCount < this.maxRetries && this.status === TASK_STATUS.FAILED;
  }

  isReadyToExecute() {
    return this.executeAt <= new Date() && 
           (this.status === TASK_STATUS.PENDING || 
            (this.status === TASK_STATUS.FAILED && this.canRetry()));
  }
}

// POST endpoint for task creation
app.post('/tasks', (req, res) => {
  try {
    const { apiUrl, payload, executeAt, method, headers } = req.body;

    // Validation
    if (!apiUrl) {
      return res.status(400).json({ 
        error: 'apiUrl is required' 
      });
    }

    if (!executeAt) {
      return res.status(400).json({ 
        error: 'executeAt is required (ISO 8601 format)' 
      });
    }

    // Validate executeAt is a valid date
    const executionTime = new Date(executeAt);
    if (isNaN(executionTime.getTime())) {
      return res.status(400).json({ 
        error: 'executeAt must be a valid ISO 8601 date string' 
      });
    }

    // Create new task
    const task = new Task(apiUrl, payload, executeAt, method, headers);
    tasks.set(task.id, task);

    res.status(201).json({
      message: 'Task created successfully',
      taskId: task.id,
      executeAt: task.executeAt,
      status: task.status
    });

  } catch (error) {
    console.error('Error creating task:', error);
    res.status(500).json({ 
      error: 'Internal server error' 
    });
  }
});

// GET endpoint for workers to query available tasks
app.get('/tasks/available', (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;
    const availableTasks = [];

    for (const task of tasks.values()) {
      if (task.isReadyToExecute() && availableTasks.length < limit) {
        availableTasks.push({
          id: task.id,
          apiUrl: task.apiUrl,
          payload: task.payload,
          method: task.method,
          headers: task.headers,
          executeAt: task.executeAt,
          status: task.status,
          retryCount: task.retryCount,
          maxRetries: task.maxRetries
        });
      }
    }

    res.json({
      tasks: availableTasks,
      count: availableTasks.length
    });

  } catch (error) {
    console.error('Error fetching available tasks:', error);
    res.status(500).json({ 
      error: 'Internal server error' 
    });
  }
});

// POST endpoint for workers to claim a task
app.post('/tasks/:taskId/claim', (req, res) => {
  try {
    const { taskId } = req.params;
    const task = tasks.get(taskId);

    if (!task) {
      return res.status(404).json({ 
        error: 'Task not found' 
      });
    }

    if (!task.isReadyToExecute()) {
      return res.status(400).json({ 
        error: 'Task is not ready to execute or already claimed' 
      });
    }

    task.claim();

    res.json({
      message: 'Task claimed successfully',
      taskId: task.id,
      claimedAt: task.claimedAt
    });

  } catch (error) {
    console.error('Error claiming task:', error);
    res.status(500).json({ 
      error: 'Internal server error' 
    });
  }
});

// PUT endpoint for workers to update task status
app.put('/tasks/:taskId/status', (req, res) => {
  try {
    const { taskId } = req.params;
    const { status, error: errorMessage, response } = req.body;
    const task = tasks.get(taskId);

    if (!task) {
      return res.status(404).json({ 
        error: 'Task not found' 
      });
    }

    switch (status) {
      case TASK_STATUS.IN_PROGRESS:
        task.markInProgress();
        break;
      case TASK_STATUS.COMPLETED:
        task.markCompleted(response);
        break;
      case TASK_STATUS.FAILED:
        task.markFailed(errorMessage);
        break;
      default:
        return res.status(400).json({ 
          error: 'Invalid status' 
        });
    }

    res.json({
      message: 'Task status updated successfully',
      taskId: task.id,
      status: task.status
    });

  } catch (error) {
    console.error('Error updating task status:', error);
    res.status(500).json({ 
      error: 'Internal server error' 
    });
  }
});

// GET endpoint to retrieve task details
app.get('/tasks/:taskId', (req, res) => {
  try {
    const { taskId } = req.params;
    const task = tasks.get(taskId);

    if (!task) {
      return res.status(404).json({ 
        error: 'Task not found' 
      });
    }

    res.json({
      id: task.id,
      apiUrl: task.apiUrl,
      payload: task.payload,
      method: task.method,
      headers: task.headers,
      executeAt: task.executeAt,
      status: task.status,
      createdAt: task.createdAt,
      claimedAt: task.claimedAt,
      completedAt: task.completedAt,
      error: task.error,
      retryCount: task.retryCount,
      maxRetries: task.maxRetries
    });

  } catch (error) {
    console.error('Error fetching task:', error);
    res.status(500).json({ 
      error: 'Internal server error' 
    });
  }
});

// GET endpoint to list all tasks with optional filtering
app.get('/tasks', (req, res) => {
  try {
    const { status, limit = 50 } = req.query;
    const taskList = [];

    for (const task of tasks.values()) {
      if (!status || task.status === status) {
        taskList.push({
          id: task.id,
          apiUrl: task.apiUrl,
          status: task.status,
          executeAt: task.executeAt,
          createdAt: task.createdAt,
          retryCount: task.retryCount
        });
      }
      
      if (taskList.length >= parseInt(limit)) {
        break;
      }
    }

    res.json({
      tasks: taskList,
      count: taskList.length,
      total: tasks.size
    });

  } catch (error) {
    console.error('Error listing tasks:', error);
    res.status(500).json({ 
      error: 'Internal server error' 
    });
  }
});

// Built-in task executor (optional - can be used for testing)
app.post('/tasks/:taskId/execute', async (req, res) => {
  try {
    const { taskId } = req.params;
    const task = tasks.get(taskId);

    if (!task) {
      return res.status(404).json({ 
        error: 'Task not found' 
      });
    }

    if (task.status !== TASK_STATUS.PENDING && task.status !== TASK_STATUS.CLAIMED) {
      return res.status(400).json({ 
        error: 'Task cannot be executed in current status' 
      });
    }

    task.markInProgress();

    try {
      const axiosConfig = {
        method: task.method,
        url: task.apiUrl,
        headers: task.headers,
        timeout: 30000 // 30 seconds timeout
      };

      if (task.method !== 'GET' && task.payload) {
        axiosConfig.data = task.payload;
      }

      const response = await axios(axiosConfig);
      
      task.markCompleted({
        status: response.status,
        data: response.data,
        headers: response.headers
      });

      res.json({
        message: 'Task executed successfully',
        taskId: task.id,
        status: task.status,
        response: {
          status: response.status,
          data: response.data
        }
      });

    } catch (apiError) {
      const errorMessage = apiError.response 
        ? `HTTP ${apiError.response.status}: ${apiError.response.statusText}`
        : apiError.message;
      
      task.markFailed(errorMessage);

      res.status(500).json({
        message: 'Task execution failed',
        taskId: task.id,
        status: task.status,
        error: errorMessage
      });
    }

  } catch (error) {
    console.error('Error executing task:', error);
    res.status(500).json({ 
      error: 'Internal server error' 
    });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    tasksCount: tasks.size
  });
});

// Start the server
app.listen(PORT, () => {
  console.log(`Task Scheduling Service running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/health`);
});

module.exports = app;
