const axios = require('axios');

const SERVER_URL = 'http://localhost:3000';

async function example() {
  console.log('=== Task Scheduling Service Example ===\n');

  try {
    // 1. Create a task to call JSONPlaceholder API
    console.log('1. Creating a task...');
    const createResponse = await axios.post(`${SERVER_URL}/tasks`, {
      apiUrl: 'https://jsonplaceholder.typicode.com/posts',
      payload: {
        title: 'My Scheduled Post',
        body: 'This post was created by a scheduled task!',
        userId: 1
      },
      executeAt: new Date().toISOString(), // Execute immediately
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const taskId = createResponse.data.taskId;
    console.log(`Task created with ID: ${taskId}\n`);

    // 2. Check available tasks
    console.log('2. Checking available tasks...');
    const availableResponse = await axios.get(`${SERVER_URL}/tasks/available`);
    console.log(`Found ${availableResponse.data.count} available tasks\n`);

    // 3. Claim the task (simulate worker behavior)
    console.log('3. Claiming the task...');
    await axios.post(`${SERVER_URL}/tasks/${taskId}/claim`);
    console.log('Task claimed successfully\n');

    // 4. Mark task as in progress
    console.log('4. Marking task as in progress...');
    await axios.put(`${SERVER_URL}/tasks/${taskId}/status`, {
      status: 'in-progress'
    });
    console.log('Task marked as in progress\n');

    // 5. Execute the task manually (simulate API call)
    console.log('5. Executing the task...');
    const executeResponse = await axios.post(`${SERVER_URL}/tasks/${taskId}/execute`);
    console.log('Task executed successfully!');
    console.log('API Response:', executeResponse.data.response.data);
    console.log();

    // 6. Check final task status
    console.log('6. Checking final task status...');
    const taskResponse = await axios.get(`${SERVER_URL}/tasks/${taskId}`);
    console.log('Task Status:', taskResponse.data.status);
    console.log('Completed At:', taskResponse.data.completedAt);
    console.log();

    console.log('=== Example completed successfully! ===');

  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

// Run the example if this file is executed directly
if (require.main === module) {
  console.log('Make sure the server is running (npm start) before running this example.\n');
  setTimeout(example, 1000);
}

module.exports = example;
